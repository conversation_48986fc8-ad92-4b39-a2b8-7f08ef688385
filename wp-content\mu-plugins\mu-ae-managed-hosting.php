<?php

	/**
	* @link              https://americaneagle.com
	* @since             1.0.0
	* @package           American_Eagle_Managed_Hosting
	*
	* @wordpress-plugin
	* Plugin Name:       American Eagle Managed Hosting
	* Plugin URI:        https://americaneagle.com
	* Description:       This plugin has custom code and features that help supplement the managed hosting system.
	* Version:           1.0.1
	* Author:            American Eagle
	* Author URI:        https://americaneagle.com
	* License:           GPL-2.0+
	* License URI:       http://www.gnu.org/licenses/gpl-2.0.txt
	* Text Domain:       american-eagle-managed-hosting
	* Domain Path:       /languages
	*/

	if ( ! defined( 'WPINC' ) ) {
		die;
	}

	define( 'AMERICAN_EAGLE_MANAGED_HOSTING_VERSION', '1.0.1' );

	class AEMH_Plugin {
		public function __construct(){
			add_action( 'admin_head', array( $this, 'hide_wfls_settings_tab' ) );
			add_action( 'plugins_loaded', array( $this, 'check_if_user_logged_in' ) );
			add_action( 'login_enqueue_scripts', array( $this, 'ae_login_page' ) );
		}

		public function check_if_user_logged_in(){
			if ( is_user_logged_in() ) {
				$current_user = wp_get_current_user();
				$email = $current_user->user_email;
				if (!current_user_can('americaneagle')) {
					$restricted_urls = array(
						'WordfenceWAF' => 'admin.php?page=WordfenceWAF&subpage=waf_options',
						'WordfenceScan' => 'admin.php?page=WordfenceScan',
						'WordfenceTools' => 'admin.php?page=WordfenceTools',
						'WordfenceOptions' => 'admin.php?page=WordfenceOptions',
						'WordfenceSupport' => 'admin.php?page=WordfenceSupport',
						'WordfenceGlobal' => 'admin.php?page=Wordfence&subpage=global_options'
					);
			
					foreach ($restricted_urls as $page => $url) {
						if (strpos($_SERVER['REQUEST_URI'], $url) !== false) {
							wp_redirect(admin_url('admin.php?page=Wordfence'));
							exit;
						}
					}
					add_filter( 'all_plugins', array( $this, 'hide_plugins' ) );
					add_filter( 'views_plugins', array( $this, 'hide_mu_plugins' ) );
					add_action( 'admin_menu', array( $this, 'hide_menu_pages' ), 999 );
					add_action( 'wp_dashboard_setup', array( $this, 'hide_dashboard_widgets' ), 999 );
				}
				$isAE = preg_match('/@americaneagle\.com/', $email);
				if( !$isAE ) {
					add_filter( 'all_plugins', array( $this, 'hide_plugins' ) );
					add_filter( 'views_plugins', array( $this, 'hide_mu_plugins' ) );
					add_action( 'admin_menu', array( $this, 'hide_menu_pages' ), 999 );
					add_action( 'wp_dashboard_setup', array( $this, 'hide_dashboard_widgets' ), 999 );
				}
			}
		}

		public function hide_wfls_settings_tab() {
			if ( !current_user_can( 'americaneagle' ) ) {
				echo '<style>#wfls-tab-settings { display: none !important; }</style>';
			}
		}

		public function hide_plugins($plugins) {
			/**
			 * Hide American Eagle Audit and Security Plugins from displaying in the Plugins page.
			 */
			$plugins_ae = array(
				'stream/stream.php',
				'wordfence/wordfence.php',
				'miniorange-saml-20-single-sign-on/login.php',
				'wp-simple-saml/plugin.php',
				'mu-ae-managed-hosting.php/file_manager_advanced.php',
				'wp-statistics/wp-statistics.php'
			);

			/**
			 * Hide black listed plugins and deactivate them if active.
			 */
			$plugins_array = array(
				'wp-statistics/wp-statistics.php', // Database intensive. Added: 08/19/2024
				'wp-file-manager/file_folder_manager.php',
				'wp-file-manager/wp-file-manager.php',
				'file-manager-advanced/file_manager_advanced.php',
				'wp-phpmyadmin-extension/index.php',
				'adminer/adminer.php',
				'async-google-analytics/async-google-analytics.php',
				'backup/backup.php',
				'backup-scheduler/backup-scheduler.php',
				'backupwordpress/backupwordpress.php',
				'backwpup/backwpup.php',
				'bad-behavior/bad-behavior.php',
				'broken-link-checker/broken-link-checker.php',
				'content-molecules/content-molecules.php',
				'contextual-related-posts/contextual-related-posts.php',
				'duplicator/duplicator.php',
				'dynamic-related-posts/dynamic-related-posts.php',
				'ezpz-one-click-backup/ezpz-one-click-backup.php',
				'file-commander/file-commander.php',
				'fuzzy-seo-booster/fuzzy-seo-booster.php',
				'gd-system-plugin/gd-system-plugin.php',
				'gd-system-plugin.php/gd-system-plugin.php.php',
				'google-xml-sitemaps-with-multisite-support/google-xml-sitemaps-with-multisite-support.php',
				'hc-custom-wp-admin-url/hc-custom-wp-admin-url.php',
				'hcs.php/hcs.php.php',
				'hello.php/hello.php.php',
				'jr-referrer/jr-referrer.php',
				'jumpple/jumpple.php',
				'missed-schedule/missed-schedule.php',
				'no-revisions/no-revisions.php',
				'ozh-who-sees-ads/ozh-who-sees-ads.php',
				'pipdig-power-pack/pipdig-power-pack.php',
				'portable-phpmyadmin/portable-phpmyadmin.php',
				'quick-cache/quick-cache.php',
				'quick-cache-pro/quick-cache-pro.php',
				'recommend-a-friend/recommend-a-friend.php',
				'seo-alrp/seo-alrp.php',
				'si-captcha-for-wordpress/si-captcha-for-wordpress.php',
				'similar-posts/similar-posts.php',
				'spamreferrerblock/spamreferrerblock.php',
				'ssclassic/ssclassic.php',
				'sspro/sspro.php',
				'super-post/super-post.php',
				'superslider/superslider.php',
				'sweetcaptcha-revolutionary-free-captcha-service/sweetcaptcha-revolutionary-free-captcha-service.php',
				'text-passwords/text-passwords.php',
				'the-codetree-backup/the-codetree-backup.php',
				'toolspack/toolspack.php',
				'ToolsPack/ToolsPack.php',
				'tweet-blender/tweet-blender.php',
				'versionpress/versionpress.php',
				'w3-total-cache/w3-total-cache.php',
				'wordpress-gzip-compression/wordpress-gzip-compression.php',
				'wp-cache/wp-cache.php',
				'wp-database-optimizer/wp-database-optimizer.php',
				'wp-db-backup/wp-db-backup.php',
				'wp-dbmanager/wp-dbmanager.php',
				'wp-engine-snapshot/wp-engine-snapshot.php',
				'wp-file-cache/wp-file-cache.php',
				'wp-phpmyadmin/wp-phpmyadmin.php',
				'wp-postviews/wp-postviews.php',
				'wp-slimstat/wp-slimstat.php',
				'wp-super-cache/wp-super-cache.php',
				'wp-symposium-alerts/wp-symposium-alerts.php',
				'wpengine-migrate/wpengine-migrate.php',
				'wpengine-migrate.tar.gz/wpengine-migrate.tar.gz.php',
				'wpengine-migrate.zip/wpengine-migrate.zip.php',
				'wpengine-snapshot/wpengine-snapshot.php',
				'wpengine-snapshot.tar.gz/wpengine-snapshot.tar.gz.php',
				'wponlinebackup/wponlinebackup.php',
			);

			foreach( $plugins_array as $plugin ) {
				if ( is_plugin_active( $plugin ) ) {
					deactivate_plugins( $plugin, true );
					unset( $plugins[$plugin] );
				}
				unset( $plugins[$plugin] );
			}
			foreach( $plugins_ae as $plugin ) {
				unset( $plugins[$plugin] );
			}
			return $plugins;
		}
		public function hide_mu_plugins( $views ) {
			if ( isset( $views['mustuse'] ) )
				unset( $views['mustuse'] );
			return $views;
		}

		public function hide_menu_pages() {
			remove_menu_page( 'wp_stream' );
			remove_menu_page( 'Wordfence' );
			remove_menu_page( 'mo_saml_settings' );
			remove_menu_page( 'WordfenceWAF' );
			remove_menu_page( 'WordfenceScan' );
			remove_menu_page( 'WordfenceTools' );
			remove_menu_page( 'WordfenceOptions' );
			remove_menu_page( 'WordfenceSupport' );
			remove_menu_page( 'wps_overview_page' );
		}

		public function hide_dashboard_widgets() {
			remove_meta_box('wordfence_activity_report_widget', 'dashboard', 'normal');
		}

		public function ae_login_page() { 
			?>
			<style type="text/css">
				#mo_saml_button > div:nth-child(2) {
					display: none;
				}

				#mo_saml_login_sso_button {
					background:  #fff !important;
					border-color: rgba(255,255,255,0) !important;
				}

				#mo_saml_login_sso_button > img {
					width: 260px !important;
					height: 50px !important;
				}

				#mo_saml_button {
					margin-bottom: 4rem;
				}

				#mo_saml_title {
					font-size: 1rem;
					font-weight: 600;
				}

				#mo_saml_wp_default_title {
					font-size: .8rem;
					font-weight: 600;
					text-align: center;
				}
				
			</style>
			<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.3/jquery.min.js"></script>
			<script type="text/javascript">
				jQuery(document).ready(function() {
					document.querySelector("#mo_saml_login_sso_button").innerHTML = '<img src="https://ameagle-assets.azureedge.net/aecom-blobs/images/default-source/default-album/logo77462a6763c4412b9e51c1748903cc85.png">"';
					jQuery("#mo_saml_button").append('<p id="mo_saml_title" style="text-align: center;">Employee Login</p><br><p id="mo_saml_wp_default_title">OR</p>');
				});
			</script>
			<?php 
		}

	}
	
	$aemh_Plugin = new AEMH_Plugin();
