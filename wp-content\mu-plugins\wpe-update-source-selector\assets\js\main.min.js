jQuery((function($){function monitorSourceSetting(){const settingSelector=".wpe-update-source-selector-source-setting input[type=radio]";const sourceSelector=".wpe-update-source-selector-preferred-source";$(settingSelector).on("change",(function(){if($(settingSelector+":checked").val()==="no"){$(sourceSelector).addClass("hidden")}else{$(sourceSelector).removeClass("hidden")}}))}function checkSourceStatus(force=false){const $errorNotice=$("#wpe-update-source-selector-source-status-error-notice");$errorNotice.addClass("hidden");$(".wpe-update-source-selector-source-status-label").addClass("hidden");$(".wpe-update-source-selector-source-status-checking").removeClass("hidden");const data={action:"wpe_uss_check_source_status",_ajax_nonce:wpeUSS.check_source_status_nonce,source_key:wpeUSS.current_source_key,force:force};$.ajax({url:ajaxurl,type:"POST",dataType:"JSON",data:data,error(jqXHR,textStatus,errorThrown){$errorNotice.removeClass("hidden")},success(response,textStatus,jqXHR){if(typeof response.success==="undefined"||typeof response.data==="undefined"){$errorNotice.removeClass("hidden");return}if(response.success===false){$errorNotice.removeClass("hidden");return}if(typeof response.data.status==="undefined"||typeof response.data.title==="undefined"){$errorNotice.removeClass("hidden");return}$(".wpe-update-source-selector-source-status-label").addClass("hidden");$(".wpe-update-source-selector-source-status-wrapper").attr("title",response.data.title);$(".wpe-update-source-selector-source-status-"+response.data.status).removeClass("hidden")}})}$(document).ready((function(){monitorSourceSetting();const sourceStatusWrapperSelector=".wpe-update-source-selector-source-status-wrapper";const sourceStatus=$(sourceStatusWrapperSelector).attr("data-source-status");if(sourceStatus==="checking"){checkSourceStatus()}$(".wpe-update-source-selector-source-status-wrapper").on("click",(function(){checkSourceStatus(true)}))}))}));