/**
 * Header.
 */

.wpe-update-source-selector-header {
	text-align: center;
	margin: 0 0 1rem;
	background: var(--wpe-update-source-selector-color-header-background);
	border-bottom: 1px solid var(--wpe-update-source-selector-color-header-border);

	/**
	 * Title.
	 */
	.wpe-update-source-selector-title-section {
		display: flex;
		align-items: center;
		justify-content: center;
		clear: both;
		padding-top: 8px;

		h1, p {
			display: inline-block;
			font-weight: 590;
			margin: 0;
			padding: 0;
			line-height: 1;
		}

		h1 {
			font-size: 24px;
			margin: 1rem 0.8rem;
		}

		p {
			font-size: 16px;
			margin: 0.5rem 0.8rem 0;
		}

		&.wpe-update-source-selector-source-status-wrapper {
			margin-bottom: 1rem;
			cursor: pointer;

			.wpe-update-source-selector-source-status {
				display: inline-block;
				height: 20px;
				width: 20px;
				margin: 0;
				border-radius: 100%;
				position: relative;
				font-weight: 600;
				font-size: 0.4rem;
			}

			.wpe-update-source-selector-source-status-label {
				font-weight: 600;
				line-height: 20px;
				margin-left: 0.3rem;

				&.dashicons-marker::before {
					color: var(--wpe-update-source-selector-color-source-status-checking);
				}

				&.dashicons-dismiss::before {
					color: var(--wpe-update-source-selector-color-source-status-error);
				}

				&.dashicons-yes-alt::before {
					color: var(--wpe-update-source-selector-color-source-status-success);
				}
			}
		}
	}

	/**
	 * Tabs.
	 */
	.wpe-update-source-selector-tabs-wrapper {
		vertical-align: top;
		display: inline-grid;
		grid-template-columns: 1fr 1fr 1fr 1fr;

		&.tab-count-3 {
			grid-template-columns: 1fr 1fr 1fr;
		}

		&.tab-count-2 {
			grid-template-columns: 1fr 1fr;
		}

		&.tab-count-1 {
			grid-template-columns: 1fr;
		}

		.wpe-update-source-selector-tab {
			display: block;
			text-decoration: none;
			color: inherit;
			padding: 0.5rem 1rem 1rem;
			margin: 0 1rem;
			transition: box-shadow 0.5s ease-in-out;

			&:focus {
				color: #1d2327;
				outline: 1px solid #787c82;
				box-shadow: none;
			}

			&.active {
				box-shadow: inset 0 -3px var(--wpe-update-source-selector-color-tab-highlight);
				font-weight: 600;
			}
		}

		.wpe-update-source-selector-offscreen-nav-wrapper {
			position: relative;
			background: transparent;
			border: none;

			.wpe-update-source-selector-offscreen-nav {
				display: none;
				position: absolute;
				padding-top: 10px;
				right: 0;
				top: 100%;
				width: 13rem;

				&::before {
					position: absolute;
					content: "";
					width: 0;
					height: 0;
					border-style: solid;
					border-width: 0 10px 5px;
					border-color: transparent transparent var(--wpe-update-source-selector-color-header-background);
					right: 20px;
					top: 5px;
				}

				.wpe-update-source-selector-tab {
					background: var(--wpe-update-source-selector-color-header-background);
					box-shadow: 0 2px 5px 0 rgb(0 0 0 / 75%);

					&.active {
						box-shadow: inset 3px 0 var(--wpe-update-source-selector-color-tab-highlight);
						font-weight: 600;
					}
				}
			}

			&:focus .wpe-update-source-selector-offscreen-nav {
				left: initial;
			}

			&.visible .wpe-update-source-selector-offscreen-nav {
				display: inline-block;
			}
		}
	}
}
