(()=>{var t={327:()=>{!function(t){var e=acf.Field.extend({type:"repeater",wait:"",page:1,nextRowNum:0,events:{'click a[data-event="add-row"]':"onClickAdd",'click a[data-event="duplicate-row"]':"onClickDuplicate",'click a[data-event="remove-row"]':"onClickRemove",'click a[data-event="collapse-row"]':"onClickCollapse",'click a[data-event="first-page"]:not(.disabled)':"onClickFirstPage",'click a[data-event="last-page"]:not(.disabled)':"onClickLastPage",'click a[data-event="prev-page"]:not(.disabled)':"onClickPrevPage",'click a[data-event="next-page"]:not(.disabled)':"onClickNextPage","change .current-page":"onChangeCurrentPage","click .acf-order-input-wrap":"onClickRowOrder","blur .acf-order-input":"onBlurRowOrder","change .acf-order-input":"onChangeRowOrder","changed:total_rows":"onChangeTotalRows",showField:"onShow",unloadField:"onUnload",mouseover:"onHover",change:"onChangeField"},$control:function(){return this.$(".acf-repeater:first")},$table:function(){return this.$("table:first")},$tbody:function(){return this.$("tbody:first")},$rows:function(){return this.$("tbody:first > tr").not(".acf-clone, .acf-deleted")},$row:function(t){return this.$("tbody:first > tr:eq("+t+")")},$clone:function(){return this.$("tbody:first > tr.acf-clone")},$actions:function(){return this.$(".acf-actions:last")},$button:function(){return this.$(".acf-actions:last .button")},$firstPageButton:function(){return this.$(".acf-tablenav:last .first-page")},$prevPageButton:function(){return this.$(".acf-tablenav:last .prev-page")},$nextPageButton:function(){return this.$(".acf-tablenav:last .next-page")},$lastPageButton:function(){return this.$(".acf-tablenav:last .last-page")},$pageInput:function(){return this.$(".current-page:last")},totalPages:function(){const t=this.$(".acf-total-pages:last").text();return parseInt(t)},getValue:function(){return this.$rows().length},allowRemove:function(){let t=this.val(),e=parseInt(this.get("min"));return this.get("pagination")&&(t=this.get("total_rows")),!e||e<t},allowAdd:function(){let t=this.val(),e=parseInt(this.get("max"));return this.get("pagination")&&(t=this.get("total_rows")),!e||e>t},addSortable:function(t){1!=this.get("max")&&(this.get("pagination")||this.$tbody().sortable({items:"> tr",handle:"> td.order",forceHelperSize:!0,forcePlaceholderSize:!0,scroll:!0,stop:function(e,a){t.render()},update:function(e,a){t.$input().trigger("change")}}))},addCollapsed:function(){var e=a.load(this.get("key"));if(!e)return!1;this.$rows().each((function(a){e.indexOf(a)>-1&&t(this).find(".-collapsed-target").length&&t(this).addClass("-collapsed")}))},addUnscopedEvents:function(e){this.on("invalidField",".acf-row",(function(a){var i=t(this);e.isCollapsed(i)&&e.expand(i)})),this.get("pagination")&&this.on("change","input, select, textarea",(function(a){const i=t(a.currentTarget);i.hasClass("acf-order-input")||i.hasClass("acf-row-status")||e.onChangeField(a,t(this))})),this.listenForSavedMetaBoxes()},initialize:function(){this.addUnscopedEvents(this),this.addCollapsed(),acf.disable(this.$clone(),this.cid),this.get("pagination")&&(this.nextRowNum=this.get("total_rows")),this.render()},render:function(e=!0){e&&this.$rows().each((function(e){t(this).find("> .order > span").html(e+1)}));var a=this.$control(),i=this.$button();0==this.val()?a.addClass("-empty"):a.removeClass("-empty"),this.allowAdd()?(a.removeClass("-max"),i.removeClass("disabled")):(a.addClass("-max"),i.addClass("disabled")),this.get("pagination")&&this.maybeDisablePagination()},listenForSavedMetaBoxes:function(){if(!acf.isGutenbergPostEditor()||!this.get("pagination"))return;let t=!0;wp.data.subscribe((()=>{wp.data.select("core/edit-post").isSavingMetaBoxes()?t=!1:t||(t=!0,this.set("total_rows",0,!0),this.ajaxLoadPage(!0))}))},incrementTotalRows:function(){let t=this.get("total_rows");this.set("total_rows",++t,!0)},decrementTotalRows:function(){let t=this.get("total_rows");this.set("total_rows",--t,!0)},validateAdd:function(){if(this.allowAdd())return!0;var t=this.get("max"),e=acf.__("Maximum rows reached ({max} rows)");return e=e.replace("{max}",t),this.showNotice({text:e,type:"warning"}),!1},onClickAdd:function(t,e){if(!this.validateAdd())return!1;e.hasClass("acf-icon")?this.add({before:e.closest(".acf-row")}):this.add()},add:function(t){if(!this.allowAdd())return!1;t=acf.parseArgs(t,{before:!1});var e=acf.duplicate({target:this.$clone(),append:this.proxy((function(e,a){t.before?t.before.before(a):e.before(a),a.removeClass("acf-clone"),acf.enable(a,this.cid)}))});if(this.get("pagination")){if(this.incrementTotalRows(),!1!==t.before){let a=parseInt(t.before.find(".acf-row-number").first().text())||0;if(!a||t.before.hasClass("acf-inserted")||t.before.hasClass("acf-added")||--a,t.before.hasClass("acf-divider")&&(t.before.removeClass("acf-divider"),e.addClass("acf-divider")),this.updateRowStatus(e,"inserted"),this.updateRowStatus(e,"reordered",a),e.find(".acf-row-number").first().hide().text(a),!e.find(".acf-order-input-wrap").hasClass("disabled")){let t=acf.__("Order will be assigned upon save");e.find(".acf-order-input-wrap").addClass("disabled"),e.find(".acf-row-number").first().after('<span title="'+t+'">-</span>')}e.find(".acf-order-input").first().hide(),e.attr("data-inserted",a)}else this.nextRowNum++,e.find(".acf-order-input").first().val(this.nextRowNum),e.find(".acf-row-number").first().text(this.nextRowNum),this.updateRowStatus(e,"added"),this.$tbody().find(".acf-divider").length||e.addClass("acf-divider");e.find(".acf-input:first").find("input:not([type=hidden]), select, textarea").first().trigger("focus")}return this.render(),this.$input().trigger("change"),e},onClickDuplicate:function(t,e){if(!this.validateAdd())return!1;var a=e.closest(".acf-row");this.duplicateRow(a)},duplicateRow:function(t){if(!this.allowAdd())return!1;var e=this.get("key"),a=acf.duplicate({target:t,rename:function(t,a,i,n){return"id"===t||"for"===t?a.replace(e+"-"+i,e+"-"+n):a.replace(e+"]["+i,e+"]["+n)},before:function(t){acf.doAction("unmount",t)},after:function(t,e){acf.doAction("remount",t)}});if(this.get("pagination")){this.incrementTotalRows();const e=parseInt(t.find(".acf-row-number").first().text())||0;if(this.updateRowStatus(a,"inserted"),this.updateRowStatus(a,"reordered",e),a.find(".acf-row-number").first().hide(),!a.find(".acf-order-input-wrap").hasClass("disabled")){let t=acf.__("Order will be assigned upon save");a.find(".acf-order-input-wrap").addClass("disabled"),a.find(".acf-row-number").first().after('<span title="'+t+'">-</span>')}a.find(".acf-order-input").first().hide(),a.attr("data-inserted",e),a.removeClass("acf-divider")}return this.$input().trigger("change"),this.render(),acf.focusAttention(a),a},validateRemove:function(){if(this.allowRemove())return!0;var t=this.get("min"),e=acf.__("Minimum rows not reached ({min} rows)");return e=e.replace("{min}",t),this.showNotice({text:e,type:"warning"}),!1},onClickRemove:function(t,e){var a=e.closest(".acf-row");if(t.shiftKey)return this.remove(a);a.addClass("-hover"),acf.newTooltip({confirmRemove:!0,target:e,context:this,confirm:function(){this.remove(a)},cancel:function(){a.removeClass("-hover")}})},onClickRowOrder:function(t,e){this.get("pagination")&&(e.hasClass("disabled")||(e.find(".acf-row-number").hide(),e.find(".acf-order-input").show().trigger("select")))},onBlurRowOrder:function(t,e){this.onChangeRowOrder(t,e,!1)},onChangeRowOrder:function(t,e,a=!0){if(!this.get("pagination"))return;const i=e.closest(".acf-row"),n=i.find(".acf-row-number").first();let s=e.val();if(i.find(".acf-order-input").first().hide(),!acf.isNumeric(s)||parseFloat(s)<0)return void n.show();s=Math.round(s);const o=s-1;e.val(s),n.text(s).show(),a&&this.updateRowStatus(i,"reordered",o)},onChangeTotalRows:function(){const t=parseInt(this.get("per_page"))||20,e=parseInt(this.get("total_rows"))||0,a=Math.ceil(e/t);this.$(".acf-total-pages:last").text(a),this.nextRowNum=e,this.page>a&&(this.page=a,this.ajaxLoadPage())},remove:function(t){const e=this;if(this.get("pagination")){if(this.decrementTotalRows(),t.data("id").includes("row-"))return this.updateRowStatus(t,"deleted"),t.hide(),e.$input().trigger("change"),void e.render(!1);t.hasClass("acf-divider")&&t.next(".acf-added").addClass("acf-divider")}acf.remove({target:t,endHeight:0,complete:function(){e.$input().trigger("change"),e.render()}})},isCollapsed:function(t){return t.hasClass("-collapsed")},collapse:function(t){t.addClass("-collapsed"),acf.doAction("hide",t,"collapse")},expand:function(t){t.removeClass("-collapsed"),acf.doAction("show",t,"collapse")},onClickCollapse:function(t,e){var a=e.closest(".acf-row"),i=this.isCollapsed(a);t.shiftKey&&(a=this.$rows()),i?this.expand(a):this.collapse(a)},onShow:function(t,e,a){var i=acf.getFields({is:":visible",parent:this.$el});acf.doAction("show_fields",i)},onUnload:function(){var e=[];this.$rows().each((function(a){t(this).hasClass("-collapsed")&&e.push(a)})),e=e.length?e:null,a.save(this.get("key"),e)},onHover:function(){this.addSortable(this),this.off("mouseover")},onChangeField:function(e,a){const i=t(e.delegateTarget);let n=a.closest(".acf-row");n.closest(".acf-field-repeater").data("key")!==i.data("key")&&(n=n.parent().closest(".acf-row")),this.updateRowStatus(n,"changed")},updateRowStatus:function(t,e,a=!0){if(!this.get("pagination"))return;const i=t.parents(".acf-field-repeater").data("key");if(this.parent()&&i!==this.get("key"))return;const n=t.data("id"),s=`${this.$el.find(".acf-repeater-hidden-input:first").attr("name")}[${n}][acf_${e}]`,o=`<input type="hidden" class="acf-row-status" name="${s}" value="${a}" />`;t.hasClass("acf-"+e)||t.addClass("acf-"+e);const r=t.find(`input[name='${s}']`);r.length?r.val(a):t.find("td").first().append(o)},onClickFirstPage:function(){this.validatePage(1)},onClickPrevPage:function(){this.validatePage(this.page-1)},onClickNextPage:function(t){this.validatePage(this.page+1)},onClickLastPage:function(){this.validatePage(this.totalPages())},onChangeCurrentPage:function(){this.validatePage(this.$pageInput().val())},maybeDisablePagination:function(){this.$actions().find(".acf-nav").removeClass("disabled"),this.page<=1&&(this.$firstPageButton().addClass("disabled"),this.$prevPageButton().addClass("disabled")),this.page>=this.totalPages()&&(this.$nextPageButton().addClass("disabled"),this.$lastPageButton().addClass("disabled"))},validatePage:function(t){const e=this;acf.validateForm({form:this.$control(),event:"",reset:!0,success:function(a){e.page=t,e.page<=1&&(e.page=1),e.page>=e.totalPages()&&(e.page=e.totalPages()),e.ajaxLoadPage()},failure:function(t){return e.$pageInput().val(e.page),!1}})},ajaxLoadPage:function(e=!1){const a=acf.prepareForAjax({action:"acf/ajax/query_repeater",paged:this.page,field_key:this.get("key"),field_name:this.get("orig_name"),field_prefix:this.get("prefix"),rows_per_page:parseInt(this.get("per_page")),refresh:e,nonce:this.get("nonce")});t.ajax({url:ajaxurl,method:"POST",dataType:"json",data:a,context:this}).done((function(a){const{rows:i}=a.data,n=this.$tbody().find("> tr");n.not(".acf-clone").hide(),e?(n.not(".acf-clone").remove(),this.set("total_rows",a.data.total_rows,!1)):n.not(".acf-changed, .acf-deleted, .acf-reordered, .acf-added, .acf-inserted, .acf-clone").remove(),Object.keys(i).forEach((e=>{let a=!1,n=this.$tbody().find("> *[data-id=row-"+e+"]"),s=this.$tbody().find("> *[data-inserted="+e+"]");if(s.length&&(s.appendTo(this.$tbody()).show(),acf.doAction("remount",s)),!n.hasClass("acf-deleted")){if(n.length)return acf.doAction("unmount",n),n.appendTo(this.$tbody()).show(),void acf.doAction("remount",n);a=t(i[e]),this.$tbody().append(a).show(),acf.doAction("remount",a),this.$clone().appendTo(this.$tbody())}}));const s=this.$tbody().find(".acf-added:hidden");if(s.length){const e=this;s.each((function(){const a=t(this);a.insertBefore(e.$clone()).show(),acf.doAction("remount",a)}))}this.$pageInput().val(this.page),this.maybeDisablePagination()})).fail((function(t,e,a){const i=acf.getXhrError(t);let n=acf.__("Error loading page");""!==i&&(n=`${n}: ${i}`),this.showNotice({text:n,type:"warning"})}))}});acf.registerFieldType(e),acf.registerConditionForFieldType("hasValue","repeater"),acf.registerConditionForFieldType("hasNoValue","repeater"),acf.registerConditionForFieldType("lessThan","repeater"),acf.registerConditionForFieldType("greaterThan","repeater");var a=new acf.Model({name:"this.collapsedRows",key:function(t,e){var a=this.get(t+e)||0;return a++,this.set(t+e,a,!0),a>1&&(t+="-"+a),t},load:function(t){t=this.key(t,"load");var e=acf.getPreference(this.name);return!(!e||!e[t])&&e[t]},save:function(e,a){e=this.key(e,"save");var i=acf.getPreference(this.name)||{};null===a?delete i[e]:i[e]=a,t.isEmptyObject(i)&&(i=null),acf.setPreference(this.name,i)}})}(jQuery)},340:()=>{!function(t){var e=acf.Field.extend({type:"flexible_content",wait:"",events:{'click [data-name="add-layout"]':"onClickAdd",'click [data-name="duplicate-layout"]':"onClickDuplicate",'click [data-name="remove-layout"]':"onClickRemove",'click [data-name="collapse-layout"]':"onClickCollapse",showField:"onShow",unloadField:"onUnload",mouseover:"onHover"},$control:function(){return this.$(".acf-flexible-content:first")},$layoutsWrap:function(){return this.$(".acf-flexible-content:first > .values")},$layouts:function(){return this.$(".acf-flexible-content:first > .values > .layout")},$layout:function(t){return this.$(".acf-flexible-content:first > .values > .layout:eq("+t+")")},$clonesWrap:function(){return this.$(".acf-flexible-content:first > .clones")},$clones:function(){return this.$(".acf-flexible-content:first > .clones  > .layout")},$clone:function(t){return this.$('.acf-flexible-content:first > .clones  > .layout[data-layout="'+t+'"]')},$actions:function(){return this.$(".acf-actions:last")},$button:function(){return this.$(".acf-actions:last .button")},$popup:function(){return this.$(".tmpl-popup:last")},getPopupHTML:function(){var e=this.$popup().html(),a=t(e),i=this;return a.find("[data-layout]").each((function(){var e=t(this),a=e.data("min")||0,n=e.data("max")||0,s=e.data("layout")||"",o=i.countLayouts(s);if(n&&o>=n)e.addClass("disabled");else if(a&&o<a){var r=a-o,l=acf.__("{required} {label} {identifier} required (min {min})"),c=acf._n("layout","layouts",r);l=(l=(l=(l=l.replace("{required}",r)).replace("{label}",s)).replace("{identifier}",c)).replace("{min}",a),e.append('<span class="badge" title="'+l+'">'+r+"</span>")}})),a.outerHTML()},getValue:function(){return this.$layouts().length},allowRemove:function(){var t=parseInt(this.get("min"));return!t||t<this.val()},allowAdd:function(){var t=parseInt(this.get("max"));return!t||t>this.val()},isFull:function(){var t=parseInt(this.get("max"));return t&&this.val()>=t},addSortable:function(t){1!=this.get("max")&&this.$layoutsWrap().sortable({items:"> .layout",handle:"> .acf-fc-layout-handle",forceHelperSize:!0,forcePlaceholderSize:!0,scroll:!0,stop:function(e,a){t.render()},update:function(e,a){t.$input().trigger("change")}})},addCollapsed:function(){var e=i.load(this.get("key"));if(!e)return!1;this.$layouts().each((function(a){e.indexOf(a)>-1&&t(this).addClass("-collapsed")}))},addUnscopedEvents:function(e){this.on("invalidField",".layout",(function(a){e.onInvalidField(a,t(this))}))},initialize:function(){this.addUnscopedEvents(this),this.addCollapsed(),acf.disable(this.$clonesWrap(),this.cid),this.render()},render:function(){this.$layouts().each((function(e){t(this).find(".acf-fc-layout-order:first").html(e+1)})),0==this.val()?this.$control().addClass("-empty"):this.$control().removeClass("-empty"),this.isFull()?this.$button().addClass("disabled"):this.$button().removeClass("disabled")},onShow:function(t,e,a){var i=acf.getFields({is:":visible",parent:this.$el});acf.doAction("show_fields",i)},countLayouts:function(e){return this.$layouts().filter((function(){return t(this).data("layout")===e})).length},countLayoutsByName:function(t){const e=t.data("max");if(!e)return!0;const a=t.data("layout")||"";if(this.countLayouts(a)>=e){let a=acf.__("This field has a limit of {max} {label} {identifier}");const i=acf._n("layout","layouts",e),n='"'+t.data("label")+'"';return a=a.replace("{max}",e),a=a.replace("{label}",n),a=a.replace("{identifier}",i),this.showNotice({text:a,type:"warning"}),!1}return!0},validateAdd:function(){if(this.allowAdd())return!0;var t=this.get("max"),e=acf.__("This field has a limit of {max} {label} {identifier}"),a=acf._n("layout","layouts",t);return e=(e=(e=e.replace("{max}",t)).replace("{label}","")).replace("{identifier}",a),this.showNotice({text:e,type:"warning"}),!1},onClickAdd:function(t,e){if(!this.validateAdd())return!1;var i=null;e.hasClass("acf-icon")&&(i=e.closest(".layout")).addClass("-hover");var n=new a({target:e,targetConfirm:!1,text:this.getPopupHTML(),context:this,confirm:function(t,e){e.hasClass("disabled")||this.add({layout:e.data("layout"),before:i})},cancel:function(){i&&i.removeClass("-hover")}});n.on("click","[data-layout]","onConfirm")},add:function(t){if(t=acf.parseArgs(t,{layout:"",before:!1}),!this.allowAdd())return!1;var e=acf.duplicate({target:this.$clone(t.layout),append:this.proxy((function(e,a){t.before?t.before.before(a):this.$layoutsWrap().append(a),acf.enable(a,this.cid),this.render()}))});return this.$input().trigger("change"),e},onClickDuplicate:function(t,e){var a=e.closest(".layout");return!!this.countLayoutsByName(a.first())&&!!this.validateAdd()&&void this.duplicateLayout(a)},duplicateLayout:function(t){if(!this.allowAdd())return!1;var e=this.get("key"),a=acf.duplicate({target:t,rename:function(t,a,i,n){return"id"===t||"for"===t?a.replace(e+"-"+i,e+"-"+n):a.replace(e+"]["+i,e+"]["+n)},before:function(t){acf.doAction("unmount",t)},after:function(t,e){acf.doAction("remount",t)}});return this.$input().trigger("change"),this.render(),acf.focusAttention(a),a},validateRemove:function(){if(this.allowRemove())return!0;var t=this.get("min"),e=acf.__("This field requires at least {min} {label} {identifier}"),a=acf._n("layout","layouts",t);return e=(e=(e=e.replace("{min}",t)).replace("{label}","")).replace("{identifier}",a),this.showNotice({text:e,type:"warning"}),!1},onClickRemove:function(t,e){var a=e.closest(".layout");if(t.shiftKey)return this.removeLayout(a);a.addClass("-hover"),acf.newTooltip({confirmRemove:!0,target:e,context:this,confirm:function(){this.removeLayout(a)},cancel:function(){a.removeClass("-hover")}})},removeLayout:function(t){var e=this,a=1==this.getValue()?60:0;acf.remove({target:t,endHeight:a,complete:function(){e.$input().trigger("change"),e.render()}})},onClickCollapse:function(t,e){var a=e.closest(".layout");this.isLayoutClosed(a)?this.openLayout(a):this.closeLayout(a)},isLayoutClosed:function(t){return t.hasClass("-collapsed")},openLayout:function(t){t.removeClass("-collapsed"),acf.doAction("show",t,"collapse")},closeLayout:function(t){t.addClass("-collapsed"),acf.doAction("hide",t,"collapse"),this.renderLayout(t)},renderLayout:function(e){var a=e.children("input").attr("name").replace("[acf_fc_layout]",""),i={action:"acf/fields/flexible_content/layout_title",field_key:this.get("key"),i:e.index(),layout:e.data("layout"),value:acf.serialize(e,a)};t.ajax({url:acf.get("ajaxurl"),data:acf.prepareForAjax(i),dataType:"html",type:"post",success:function(t){t&&e.children(".acf-fc-layout-handle").html(t)}})},onUnload:function(){var e=[];this.$layouts().each((function(a){t(this).hasClass("-collapsed")&&e.push(a)})),e=e.length?e:null,i.save(this.get("key"),e)},onInvalidField:function(t,e){this.isLayoutClosed(e)&&this.openLayout(e)},onHover:function(){this.addSortable(this),this.off("mouseover")}});acf.registerFieldType(e);var a=acf.models.TooltipConfirm.extend({events:{"click [data-layout]":"onConfirm",'click [data-event="cancel"]':"onCancel"},render:function(){this.html(this.get("text")),this.$el.addClass("acf-fc-popup")}});acf.registerConditionForFieldType("hasValue","flexible_content"),acf.registerConditionForFieldType("hasNoValue","flexible_content"),acf.registerConditionForFieldType("lessThan","flexible_content"),acf.registerConditionForFieldType("greaterThan","flexible_content");var i=new acf.Model({name:"this.collapsedLayouts",key:function(t,e){var a=this.get(t+e)||0;return a++,this.set(t+e,a,!0),a>1&&(t+="-"+a),t},load:function(t){t=this.key(t,"load");var e=acf.getPreference(this.name);return!(!e||!e[t])&&e[t]},save:function(e,a){e=this.key(e,"save");var i=acf.getPreference(this.name)||{};null===a?delete i[e]:i[e]=a,t.isEmptyObject(i)&&(i=null),acf.setPreference(this.name,i)}})}(jQuery)},349:()=>{var t,e;t=jQuery,e=acf.Field.extend({type:"gallery",events:{"click .acf-gallery-add":"onClickAdd","click .acf-gallery-edit":"onClickEdit","click .acf-gallery-remove":"onClickRemove","click .acf-gallery-attachment":"onClickSelect","click .acf-gallery-close":"onClickClose","change .acf-gallery-sort":"onChangeSort","click .acf-gallery-update":"onUpdate",mouseover:"onHover",showField:"render"},actions:{validation_begin:"onValidationBegin",validation_failure:"onValidationFailure",resize:"onResize"},onValidationBegin:function(){acf.disable(this.$sideData(),this.cid)},onValidationFailure:function(){acf.enable(this.$sideData(),this.cid)},$control:function(){return this.$(".acf-gallery")},$collection:function(){return this.$(".acf-gallery-attachments")},$attachments:function(){return this.$(".acf-gallery-attachment")},$attachment:function(t){return this.$('.acf-gallery-attachment[data-id="'+t+'"]')},$active:function(){return this.$(".acf-gallery-attachment.active")},$main:function(){return this.$(".acf-gallery-main")},$side:function(){return this.$(".acf-gallery-side")},$sideData:function(){return this.$(".acf-gallery-side-data")},isFull:function(){var t=parseInt(this.get("max")),e=this.$attachments().length;return t&&e>=t},getValue:function(){var e=[];return this.$attachments().each((function(){e.push(t(this).data("id"))})),!!e.length&&e},addUnscopedEvents:function(e){this.on("change",".acf-gallery-side",(function(a){e.onUpdate(a,t(this))}))},addSortable:function(t){this.$collection().sortable({items:".acf-gallery-attachment",forceHelperSize:!0,forcePlaceholderSize:!0,scroll:!0,start:function(t,e){e.placeholder.html(e.item.html()),e.placeholder.removeAttr("style")},update:function(e,a){t.$input().trigger("change")}}),this.$control().resizable({handles:"s",minHeight:200,stop:function(t,e){acf.update_user_setting("gallery_height",e.size.height)}})},initialize:function(){this.addUnscopedEvents(this),this.render()},render:function(){var t=this.$(".acf-gallery-sort"),e=this.$(".acf-gallery-add"),a=this.$attachments().length;this.isFull()?e.addClass("disabled"):e.removeClass("disabled"),a?t.removeClass("disabled"):t.addClass("disabled"),this.resize()},resize:function(){var t=this.$control().width(),e=Math.round(t/150);e=Math.min(e,8),this.$control().attr("data-columns",e)},onResize:function(){this.resize()},openSidebar:function(){this.$control().addClass("-open");var t=this.$control().width()/3;t=parseInt(t),t=Math.max(t,350),this.$(".acf-gallery-side-inner").css({width:t-1}),this.$side().animate({width:t-1},250),this.$main().animate({right:t},250)},closeSidebar:function(){this.$control().removeClass("-open"),this.$active().removeClass("active"),acf.disable(this.$side());var t=this.$(".acf-gallery-side-data");this.$main().animate({right:0},250),this.$side().animate({width:0},250,(function(){t.html("")}))},onClickAdd:function(e,a){this.isFull()?this.showNotice({text:acf.__("Maximum selection reached"),type:"warning"}):acf.newMediaPopup({mode:"select",title:acf.__("Add Image to Gallery"),field:this.get("key"),multiple:"add",library:this.get("library"),allowedTypes:this.get("mime_types"),selected:this.val(),select:t.proxy((function(t,e){this.appendAttachment(t,e)}),this)})},appendAttachment:function(e,a){if(e=this.validateAttachment(e),!this.isFull()&&!this.$attachment(e.id).length){var i=['<div class="acf-gallery-attachment" data-id="'+e.id+'">','<input type="hidden" value="'+e.id+'" name="'+this.getInputName()+'[]">','<div class="margin" title="">','<div class="thumbnail">','<img src="" alt="">',"</div>",'<div class="filename"></div>',"</div>",'<div class="actions">','<a href="#" class="acf-icon -cancel dark acf-gallery-remove" data-id="'+e.id+'"></a>',"</div>","</div>"].join(""),n=t(i);if(this.$collection().append(n),"prepend"===this.get("insert")){var s=this.$attachments().eq(a);s.length&&s.before(n)}this.renderAttachment(e),this.render(),this.$input().trigger("change")}},validateAttachment:function(t){if((t=acf.parseArgs(t,{id:"",url:"",alt:"",title:"",filename:"",type:"image"})).attributes){t=t.attributes;var e=acf.isget(t,"sizes",this.get("preview_size"),"url");null!==e&&(t.url=e)}return t},renderAttachment:function(t){t=this.validateAttachment(t);var e=this.$attachment(t.id);if("image"==t.type)e.find(".filename").remove();else{var a=acf.isget(t,"image","src");null!==a&&(t.url=a),e.find(".filename").text(t.filename)}t.url||(t.url=acf.get("mimeTypeIcon"),e.addClass("-icon")),e.find("img").attr({src:t.url,alt:t.alt,title:t.title}),acf.val(e.find("input"),t.id)},editAttachment:function(e){acf.newMediaPopup({mode:"edit",title:acf.__("Edit Image"),button:acf.__("Update Image"),attachment:e,field:this.get("key"),select:t.proxy((function(t,e){this.renderAttachment(t)}),this)})},onClickEdit:function(t,e){var a=e.data("id");a&&this.editAttachment(a)},removeAttachment:function(t){this.closeSidebar(),this.$attachment(t).remove(),this.render(),this.$input().trigger("change")},onClickRemove:function(t,e){t.preventDefault(),t.stopPropagation();var a=e.data("id");a&&this.removeAttachment(a)},selectAttachment:function(e){var a=this.$attachment(e);if(!a.hasClass("active")){var i=this.proxy((function(){this.$side().find(":focus").trigger("blur"),this.$active().removeClass("active"),a.addClass("active"),this.openSidebar(),n()})),n=this.proxy((function(){const a={action:"acf/fields/gallery/get_attachment",nonce:this.get("nonce"),field_key:this.get("key"),id:e};this.has("xhr")&&this.get("xhr").abort(),acf.showLoading(this.$sideData());var i=t.ajax({url:acf.get("ajaxurl"),data:acf.prepareForAjax(a),type:"post",dataType:"html",cache:!1,success:s});this.set("xhr",i)})),s=this.proxy((function(t){if(t){var e=this.$sideData();e.html(t),e.find(".compat-field-acf-form-data").remove(),e.find("> table.form-table > tbody").append(e.find("> .compat-attachment-fields > tbody > tr")),acf.doAction("append",e)}}));i()}},onClickSelect:function(t,e){var a=e.data("id");a&&this.selectAttachment(a)},onClickClose:function(t,e){this.closeSidebar()},onChangeSort:function(e,a){if(!a.hasClass("disabled")){var i=a.val();if(i){var n=[];this.$attachments().each((function(){n.push(t(this).data("id"))}));var s=this.proxy((function(){const e={action:"acf/fields/gallery/get_sort_order",nonce:this.get("nonce"),field_key:this.get("key"),ids:n,sort:i};t.ajax({url:acf.get("ajaxurl"),dataType:"json",type:"post",cache:!1,data:acf.prepareForAjax(e),success:o})})),o=this.proxy((function(t){acf.isAjaxSuccess(t)&&(t.data.reverse(),t.data.map((function(t){this.$collection().prepend(this.$attachment(t))}),this))}));s()}}},onUpdate:function(e,a){var i=this.$(".acf-gallery-update");if(i.hasClass("disabled"))return;const n=acf.serialize(this.$sideData());i.addClass("disabled"),i.before('<i class="acf-loading"></i> '),n.action="acf/fields/gallery/update_attachment",n.nonce=this.get("nonce"),n.field_key=this.get("key"),t.ajax({url:acf.get("ajaxurl"),data:acf.prepareForAjax(n),type:"post",dataType:"json",complete:function(){i.removeClass("disabled"),i.prev(".acf-loading").remove()}})},onHover:function(){this.addSortable(this),this.off("mouseover")}}),acf.registerFieldType(e),acf.registerConditionForFieldType("hasValue","gallery"),acf.registerConditionForFieldType("hasNoValue","gallery"),acf.registerConditionForFieldType("selectionLessThan","gallery"),acf.registerConditionForFieldType("selectionGreaterThan","gallery")}},e={};function a(i){var n=e[i];if(void 0!==n)return n.exports;var s=e[i]={exports:{}};return t[i](s,s.exports,a),s.exports}a.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return a.d(e,{a:e}),e},a.d=(t,e)=>{for(var i in e)a.o(e,i)&&!a.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},a.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),(()=>{"use strict";a(327),a(340),a(349)})()})();