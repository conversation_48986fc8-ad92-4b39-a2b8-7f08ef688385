# Руководство по реализации команды

## Что было создано

### 1. ACF Options Page
- **Местоположение**: Админка → Team Settings
- **Назначение**: Управление настройками отображения команды

### 2. ACF Fields для Team Settings
- **Executive Team Members** - выбор участников Executive команды
- **Executive Section Title** - заголовок секции Executive (по умолчанию: "Executive Team")
- **Main Team Section Title** - заголовок основной секции (по умолчанию: "Our Team")

### 3. Функции PHP
- `get_executive_team_members()` - получить Executive участников
- `get_executive_team_member_ids()` - получить ID Executive участников
- `executive_team_shortcode()` - шорткод для отображения Executive команды

### 4. Фильтры
- Исключение Executive участников из Search & Filter Pro
- Исключение Executive участников из обычных WordPress запросов

## Как использовать

### Шаг 1: Настройка Executive команды
1. Зайдите в **Админка → Team Settings**
2. В поле **"Executive Team Members"** выберите участников команды
3. При необходимости измените заголовки секций
4. Сохраните изменения

### Шаг 2: Добавление на страницу команды

#### Вариант A: Использование шорткода
Добавьте в начало страницы команды:
```
[executive_team]
```

#### Параметры шорткода:
- `columns="3"` - количество колонок (2, 3, 4)
- `show_title="true"` - показывать заголовок секции
- `class="custom-class"` - дополнительный CSS класс

#### Примеры:
```
[executive_team columns="2" show_title="false"]
[executive_team columns="4" class="my-executive-section"]
```

#### Вариант B: Использование в PHP шаблоне
```php
<?php echo do_shortcode('[executive_team]'); ?>
```

### Шаг 3: Настройка основного фильтра команды

#### Вариант A: Search & Filter Pro (рекомендуется)
1. Создайте или настройте Search & Filter Pro форму для post type "team-member"
2. Executive участники автоматически исключатся из результатов
3. Фильтры будут работать только с остальными участниками команды

#### Вариант B: Встроенный шорткод (если нет S&F Pro)
Добавьте после Executive секции:
```
[main_team]
```

#### Параметры шорткода main_team:
- `columns="3"` - количество колонок (2, 3, 4)
- `posts_per_page="12"` - количество участников на странице
- `show_title="true"` - показывать заголовок секции
- `show_filters="true"` - показывать простые фильтры
- `class="custom-class"` - дополнительный CSS класс

#### Примеры:
```
[main_team columns="4" posts_per_page="20"]
[main_team show_filters="true" columns="3"]
[main_team show_title="false" class="compact-team"]
```

## Структура страницы команды

```
┌─────────────────────────────────────┐
│           Executive Team            │
│    [executive_team shortcode]       │
│                                     │
│  ┌─────┐  ┌─────┐  ┌─────┐         │
│  │ CEO │  │ CTO │  │ CFO │         │
│  └─────┘  └─────┘  └─────┘         │
└─────────────────────────────────────┘
              ↓
┌─────────────────────────────────────┐
│            Our Team                 │
│     [Search & Filter Pro]           │
│                                     │
│  Фильтры: Position, Department      │
│  ┌─────┐  ┌─────┐  ┌─────┐         │
│  │ Dev │  │ HR  │  │ Sales│        │
│  └─────┘  └─────┘  └─────┘         │
└─────────────────────────────────────┘
```

## Преимущества решения

✅ **Четкое разделение**: Executive команда всегда вверху
✅ **Гибкость**: Легко управлять через админку
✅ **Автоматическое исключение**: Executive не дублируются в фильтрах
✅ **Адаптивность**: Responsive дизайн
✅ **Совместимость**: Работает с S&F Pro и Kadence blocks

## Следующие шаги

1. **Настроить Team Settings** в админке
2. **Добавить шорткод** на страницу команды
3. **Настроить Search & Filter Pro** для основной команды
4. **Протестировать** работу фильтров

## Кастомизация стилей

Стили можно изменить через CSS:
- `.executive-team-section` - основной контейнер
- `.executive-team-title` - заголовок секции
- `.executive-team-grid` - сетка участников
- `.executive-team-member` - карточка участника
- `.member-photo`, `.member-name`, `.member-title` - элементы карточки
