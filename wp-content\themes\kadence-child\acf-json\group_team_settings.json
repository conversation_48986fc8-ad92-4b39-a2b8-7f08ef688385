{"key": "group_team_settings", "title": "Team Settings", "fields": [{"key": "field_executive_team_members", "label": "Executive Team Members", "name": "executive_team_members", "aria-label": "", "type": "relationship", "instructions": "Select team members to display in the Executive section at the top of the team page. These members will be shown separately from the filtered team list.", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "post_type": ["team-member"], "post_status": ["publish"], "taxonomy": [], "filters": ["search"], "return_format": "object", "min": "", "max": "", "elements": ["featured_image"], "bidirectional": 0, "bidirectional_target": []}, {"key": "field_executive_section_title", "label": "Executive Section Title", "name": "executive_section_title", "aria-label": "", "type": "text", "instructions": "Title for the Executive team section (default: \"Executive Team\")", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "Executive Team", "maxlength": "", "placeholder": "Executive Team", "prepend": "", "append": ""}, {"key": "field_main_team_section_title", "label": "Main Team Section Title", "name": "main_team_section_title", "aria-label": "", "type": "text", "instructions": "Title for the main team section with filters (default: \"Our Team\")", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "Our Team", "maxlength": "", "placeholder": "Our Team", "prepend": "", "append": ""}], "location": [[{"param": "options_page", "operator": "==", "value": "team-settings"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "Settings for managing team member display and Executive team selection.", "show_in_rest": 0}