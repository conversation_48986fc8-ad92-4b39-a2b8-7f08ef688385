{"key": "post_type_684c7d61ef9c4", "title": "Team Members", "menu_order": 0, "active": true, "post_type": "team-member", "advanced_configuration": true, "import_source": "", "import_date": "", "labels": {"name": "Team Members", "singular_name": "Team Member", "menu_name": "Team Members", "all_items": "All Team Members", "edit_item": "Edit Team Member", "view_item": "View Team Member", "view_items": "View Team Members", "add_new_item": "Add New Team Member", "add_new": "Add New Team Member", "new_item": "New Team Member", "parent_item_colon": "Parent Team Member:", "search_items": "Search Team Members", "not_found": "No team members found", "not_found_in_trash": "No team members found in Trash", "archives": "Team Member Archives", "attributes": "Team Member Attributes", "featured_image": "", "set_featured_image": "", "remove_featured_image": "", "use_featured_image": "", "insert_into_item": "Insert into team member", "uploaded_to_this_item": "Uploaded to this team member", "filter_items_list": "Filter team members list", "filter_by_date": "Filter team members by date", "items_list_navigation": "Team Members list navigation", "items_list": "Team Members list", "item_published": "Team Member published.", "item_published_privately": "Team Member published privately.", "item_reverted_to_draft": "Team Member reverted to draft.", "item_scheduled": "Team Member scheduled.", "item_updated": "Team Member updated.", "item_link": "Team Member <PERSON>", "item_link_description": "A link to a team member."}, "description": "", "public": true, "hierarchical": false, "exclude_from_search": false, "publicly_queryable": true, "show_ui": true, "show_in_menu": true, "admin_menu_parent": "", "show_in_admin_bar": true, "show_in_nav_menus": true, "show_in_rest": true, "rest_base": "", "rest_namespace": "wp/v2", "rest_controller_class": "WP_REST_Posts_Controller", "menu_position": "", "menu_icon": {"type": "dashicons", "value": "dashicons-admin-post"}, "rename_capabilities": false, "singular_capability_name": "post", "plural_capability_name": "posts", "supports": ["title", "editor", "thumbnail", "custom-fields"], "taxonomies": "", "has_archive": false, "has_archive_slug": "", "rewrite": {"permalink_rewrite": "post_type_key", "with_front": "1", "feeds": "0", "pages": "1"}, "query_var": "post_type_key", "query_var_name": "", "can_export": true, "delete_with_user": false, "register_meta_box_cb": "", "enter_title_here": "", "modified": 1751884779}