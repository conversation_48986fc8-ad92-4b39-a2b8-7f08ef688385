/**
 * Team Member Filtering JavaScript
 * Handles filtering functionality for main team shortcode
 */
document.addEventListener('DOMContentLoaded', function() {
    const filterBtns = document.querySelectorAll('.filter-btn');
    const teamMembers = document.querySelectorAll('.main-team-member');

    // Only proceed if we have filter buttons and team members
    if (filterBtns.length === 0 || teamMembers.length === 0) {
        return;
    }

    filterBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const filter = this.getAttribute('data-filter');

            // Update active button
            filterBtns.forEach(b => b.classList.remove('active'));
            this.classList.add('active');

            // Filter members
            teamMembers.forEach(member => {
                if (filter === 'all') {
                    member.style.display = 'block';
                } else {
                    // Get all possible class variations
                    const filterVariations = [
                        filter.toLowerCase().replace(/\s+/g, '-'),
                        filter.toLowerCase().replace(/\s+/g, ''),
                        filter.toLowerCase(),
                        filter
                    ];

                    let hasPosition = false;

                    // Check if member has any of the position classes
                    filterVariations.forEach(variation => {
                        if (member.classList.contains(variation)) {
                            hasPosition = true;
                        }
                    });

                    // Also check if position text is in member positions paragraph
                    const positionsText = member.querySelector('.member-positions');
                    if (positionsText && positionsText.textContent.includes(filter)) {
                        hasPosition = true;
                    }

                    // Show or hide member based on filter match
                    member.style.display = hasPosition ? 'block' : 'none';
                }
            });
        });
    });
});
