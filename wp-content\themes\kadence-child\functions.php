<?php

/**
 * Enqueue child styles.
 */

function child_enqueue_styles() {
	wp_enqueue_style( 'child-theme', get_stylesheet_directory_uri() . '/style.css', array(), null );
}
add_action( 'wp_enqueue_scripts', 'child_enqueue_styles', 15 );

/**
 * Load ACF field groups and post types from local files
 */
function load_acf_local_files() {
    // Load ACF field groups
    $acf_path = get_stylesheet_directory() . '/acf-json/';
    if (is_dir($acf_path)) {
        add_filter('acf/settings/load_json', function($paths) use ($acf_path) {
            $paths[] = $acf_path;
            return $paths;
        });
    }
}
add_action('acf/init', 'load_acf_local_files');

/**
 * Set ACF to save field groups to local JSON files automatically
 */
add_filter('acf/settings/save_json', function($path) {
    return get_stylesheet_directory() . '/acf-json';
});

/**
 * Add custom save path for ACF field groups
 */
add_filter('acf/settings/load_json', function($paths) {
    // Remove original path (optional)
    unset($paths[0]);

    // Add new path
    $paths[] = get_stylesheet_directory() . '/acf-json';

    return $paths;
});

/**
 * Create ACF Options Page for Team Settings
 */
if( function_exists('acf_add_options_page') ) {
    acf_add_options_page(array(
        'page_title' => 'Team Settings',
        'menu_title' => 'Team Settings',
        'menu_slug' => 'team-settings',
        'capability' => 'edit_posts',
        'icon_url' => 'dashicons-groups',
        'position' => 30,
    ));
}

/**
 * Get Executive Team Members
 */
function get_executive_team_members() {
    $executive_members = get_field('executive_team_members', 'option');
    return $executive_members ? $executive_members : array();
}

/**
 * Get Executive Team Member IDs
 */
function get_executive_team_member_ids() {
    $executive_members = get_executive_team_members();
    $ids = array();

    if ($executive_members) {
        foreach ($executive_members as $member) {
            $ids[] = $member->ID;
        }
    }

    return $ids;
}

/**
 * Shortcode for displaying Executive Team
 */
function executive_team_shortcode($atts) {
    $atts = shortcode_atts(array(
        'columns' => '3',
        'show_title' => 'true',
        'class' => ''
    ), $atts);

    $executive_members = get_executive_team_members();
    $section_title = get_field('executive_section_title', 'option') ?: 'Executive Team';

    if (empty($executive_members)) {
        return '';
    }

    ob_start();
    ?>
    <div class="executive-team-section <?php echo esc_attr($atts['class']); ?>">
        <?php if ($atts['show_title'] === 'true'): ?>
            <h2 class="executive-team-title"><?php echo esc_html($section_title); ?></h2>
        <?php endif; ?>

        <div class="executive-team-grid columns-<?php echo esc_attr($atts['columns']); ?>">
            <?php foreach ($executive_members as $member): ?>
                <div class="executive-team-member">
                    <?php if (has_post_thumbnail($member->ID)): ?>
                        <div class="member-photo">
                            <?php echo get_the_post_thumbnail($member->ID, 'medium'); ?>
                        </div>
                    <?php endif; ?>

                    <div class="member-info">
                        <h3 class="member-name"><?php echo esc_html($member->post_title); ?></h3>

                        <?php
                        $title = get_field('team_member_title', $member->ID);
                        if ($title): ?>
                            <p class="member-title"><?php echo esc_html($title); ?></p>
                        <?php endif; ?>

                        <?php
                        $positions = get_field('team_member_position', $member->ID);
                        if ($positions): ?>
                            <p class="member-positions">
                                <?php
                                if (is_array($positions)) {
                                    echo esc_html(implode(', ', $positions));
                                } else {
                                    echo esc_html($positions);
                                }
                                ?>
                            </p>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>

    <style>
    .executive-team-section {
        margin-bottom: 3rem;
        padding-bottom: 2rem;
        border-bottom: 2px solid #e5e5e5;
    }

    .executive-team-title {
        text-align: center;
        margin-bottom: 2rem;
        font-size: 2.5rem;
        font-weight: bold;
    }

    .executive-team-grid {
        display: grid;
        gap: 2rem;
        margin-bottom: 2rem;
    }

    .executive-team-grid.columns-2 {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }

    .executive-team-grid.columns-3 {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    .executive-team-grid.columns-4 {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    .executive-team-member {
        text-align: center;
        background: #f9f9f9;
        padding: 1.5rem;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .member-photo img {
        width: 100%;
        height: auto;
        border-radius: 50%;
        max-width: 200px;
        margin-bottom: 1rem;
    }

    .member-name {
        font-size: 1.25rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
        color: #333;
    }

    .member-title {
        font-size: 1rem;
        color: #666;
        margin-bottom: 0.5rem;
        font-weight: 500;
    }

    .member-positions {
        font-size: 0.9rem;
        color: #888;
        font-style: italic;
    }
    </style>
    <?php

    return ob_get_clean();
}
add_shortcode('executive_team', 'executive_team_shortcode');

/**
 * Modify Search & Filter Pro query to exclude Executive team members
 */
function exclude_executive_from_team_filter($query_args, $sfid) {
    // Only apply to team member queries
    if (isset($query_args['post_type']) && $query_args['post_type'] === 'team-member') {
        $executive_ids = get_executive_team_member_ids();

        if (!empty($executive_ids)) {
            $query_args['post__not_in'] = $executive_ids;
        }
    }

    return $query_args;
}
add_filter('sf_edit_query_args', 'exclude_executive_from_team_filter', 20, 2);

/**
 * Exclude Executive team members from regular team member queries
 * (for Kadence blocks or other queries)
 */
function exclude_executive_from_regular_team_queries($query) {
    // Only on frontend and for team-member post type
    if (!is_admin() && !$query->is_main_query() && $query->get('post_type') === 'team-member') {
        $executive_ids = get_executive_team_member_ids();

        if (!empty($executive_ids)) {
            $existing_exclude = $query->get('post__not_in');
            if (is_array($existing_exclude)) {
                $executive_ids = array_merge($existing_exclude, $executive_ids);
            }
            $query->set('post__not_in', $executive_ids);
        }
    }
}
add_action('pre_get_posts', 'exclude_executive_from_regular_team_queries');

/**
 * Add custom body class when on team page with executive members
 */
function add_team_page_body_class($classes) {
    if (is_page() && (strpos(get_the_content(), '[executive_team]') !== false)) {
        $classes[] = 'has-executive-team';
    }
    return $classes;
}
add_filter('body_class', 'add_team_page_body_class');

/**
 * Enqueue team-specific styles
 */
function enqueue_team_styles() {
    if (is_page() && (strpos(get_the_content(), '[executive_team]') !== false)) {
        wp_add_inline_style('kadence-base', '
            .has-executive-team .executive-team-section {
                margin-bottom: 4rem;
                padding-bottom: 3rem;
                border-bottom: 3px solid #e5e5e5;
            }

            @media (max-width: 768px) {
                .executive-team-grid {
                    grid-template-columns: 1fr !important;
                    gap: 1.5rem;
                }

                .executive-team-title {
                    font-size: 2rem;
                }

                .executive-team-member {
                    padding: 1rem;
                }
            }
        ');
    }
}
add_action('wp_enqueue_scripts', 'enqueue_team_styles');

/**
 * Shortcode for displaying main team (excluding Executive)
 * Useful as fallback if Search & Filter Pro is not available
 */
function main_team_shortcode($atts) {
    $atts = shortcode_atts(array(
        'columns' => '3',
        'posts_per_page' => '12',
        'show_title' => 'true',
        'show_filters' => 'false',
        'class' => ''
    ), $atts);

    $section_title = get_field('main_team_section_title', 'option') ?: 'Our Team';
    $executive_ids = get_executive_team_member_ids();

    $query_args = array(
        'post_type' => 'team-member',
        'posts_per_page' => intval($atts['posts_per_page']),
        'post_status' => 'publish',
        'orderby' => 'menu_order title',
        'order' => 'ASC'
    );

    if (!empty($executive_ids)) {
        $query_args['post__not_in'] = $executive_ids;
    }

    $team_query = new WP_Query($query_args);

    if (!$team_query->have_posts()) {
        return '<p>No team members found.</p>';
    }

    ob_start();
    ?>
    <div class="main-team-section <?php echo esc_attr($atts['class']); ?>">
        <?php if ($atts['show_title'] === 'true'): ?>
            <h2 class="main-team-title"><?php echo esc_html($section_title); ?></h2>
        <?php endif; ?>

        <?php if ($atts['show_filters'] === 'true'): ?>
            <div class="team-filters">
                <button class="filter-btn active" data-filter="all">All</button>
                <button class="filter-btn" data-filter="Executive">Executive</button>
                <button class="filter-btn" data-filter="Senior Management">Senior Management</button>
                <button class="filter-btn" data-filter="Managing Director">Managing Director</button>
            </div>
        <?php endif; ?>

        <div class="main-team-grid columns-<?php echo esc_attr($atts['columns']); ?>">
            <?php while ($team_query->have_posts()): $team_query->the_post(); ?>
                <?php
                $positions = get_field('team_member_position');
                $position_classes = '';
                if (is_array($positions)) {
                    $classes = array();
                    foreach ($positions as $position) {
                        $classes[] = sanitize_html_class(strtolower(str_replace(' ', '-', $position)));
                        $classes[] = sanitize_html_class(strtolower($position));
                        $classes[] = sanitize_html_class($position);
                    }
                    $position_classes = implode(' ', array_unique($classes));
                } elseif ($positions) {
                    $position_classes = sanitize_html_class(strtolower(str_replace(' ', '-', $positions))) . ' ' .
                                       sanitize_html_class(strtolower($positions)) . ' ' .
                                       sanitize_html_class($positions);
                }
                ?>
                <div class="main-team-member <?php echo esc_attr($position_classes); ?>">
                    <?php if (has_post_thumbnail()): ?>
                        <div class="member-photo">
                            <?php the_post_thumbnail('medium'); ?>
                        </div>
                    <?php endif; ?>

                    <div class="member-info">
                        <h3 class="member-name"><?php the_title(); ?></h3>

                        <?php
                        $title = get_field('team_member_title');
                        if ($title): ?>
                            <p class="member-title"><?php echo esc_html($title); ?></p>
                        <?php endif; ?>

                        <?php if ($positions): ?>
                            <p class="member-positions">
                                <?php
                                if (is_array($positions)) {
                                    echo esc_html(implode(', ', $positions));
                                } else {
                                    echo esc_html($positions);
                                }
                                ?>
                            </p>
                            <!-- Debug info (remove in production) -->
                            <small style="color: #999; font-size: 0.8em;">
                                Classes: <?php echo esc_html($position_classes); ?>
                            </small>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endwhile; ?>
        </div>
    </div>

    <?php if ($atts['show_filters'] === 'true'): ?>
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const filterBtns = document.querySelectorAll('.filter-btn');
        const teamMembers = document.querySelectorAll('.main-team-member');

        console.log('Found filter buttons:', filterBtns.length);
        console.log('Found team members:', teamMembers.length);

        filterBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const filter = this.getAttribute('data-filter');
                console.log('Filtering by:', filter);

                // Update active button
                filterBtns.forEach(b => b.classList.remove('active'));
                this.classList.add('active');

                // Filter members
                teamMembers.forEach(member => {
                    if (filter === 'all') {
                        member.style.display = 'block';
                    } else {
                        // Get all possible class variations
                        const filterVariations = [
                            filter.toLowerCase().replace(/\s+/g, '-'),
                            filter.toLowerCase().replace(/\s+/g, ''),
                            filter.toLowerCase(),
                            filter
                        ];

                        let hasPosition = false;
                        filterVariations.forEach(variation => {
                            if (member.classList.contains(variation)) {
                                hasPosition = true;
                            }
                        });

                        // Also check if position text is in member positions paragraph
                        const positionsText = member.querySelector('.member-positions');
                        if (positionsText && positionsText.textContent.includes(filter)) {
                            hasPosition = true;
                        }

                        console.log('Member classes:', member.className, 'Has position:', hasPosition);

                        if (hasPosition) {
                            member.style.display = 'block';
                        } else {
                            member.style.display = 'none';
                        }
                    }
                });
            });
        });
    });
    </script>

    <style>
    .team-filters {
        text-align: center;
        margin-bottom: 2rem;
    }

    .filter-btn {
        background: #f0f0f0;
        border: 1px solid #ddd;
        padding: 0.5rem 1rem;
        margin: 0 0.25rem;
        cursor: pointer;
        border-radius: 4px;
        transition: all 0.3s ease;
    }

    .filter-btn:hover,
    .filter-btn.active {
        background: #007cba;
        color: white;
        border-color: #007cba;
    }

    .main-team-grid {
        display: grid;
        gap: 2rem;
    }

    .main-team-grid.columns-2 {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }

    .main-team-grid.columns-3 {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    .main-team-grid.columns-4 {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    .main-team-member {
        text-align: center;
        background: #fff;
        padding: 1.5rem;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        transition: transform 0.3s ease;
    }

    .main-team-member:hover {
        transform: translateY(-5px);
    }
    </style>
    <?php endif; ?>

    <?php
    wp_reset_postdata();
    return ob_get_clean();
}
add_shortcode('main_team', 'main_team_shortcode');

function remove_global_css() {
	remove_action( 'wp_enqueue_scripts', 'wp_enqueue_global_styles' );
	remove_action( 'wp_body_open', 'wp_global_styles_render_svg_filters' );
	remove_action( 'wp_footer', 'wp_enqueue_global_styles', 1 );
}
add_action('init', 'remove_global_css');

// Shortcode for dynamic year - use [year] in footer or as needed
function year_shortcode() {
  $year = date('Y');
  return $year;
}
add_shortcode('year', 'year_shortcode');

/* Update default GF error message gform_validation_message_FORMNUMBER */
add_filter( 'gform_validation_message_1', function ( $message, $form ) {
    if ( gf_upgrade()->get_submissions_block() ) {
        return $message;
    }
 
    $message = "<h2 class='gform_submission_error hide_summary'>Email address is required. Please <NAME_EMAIL> format</h2>";
   
 
    return $message;
}, 10, 2 );