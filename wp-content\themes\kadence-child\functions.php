<?php

/**
 * Enqueue child styles.
 */

function child_enqueue_styles() {
	wp_enqueue_style( 'child-theme', get_stylesheet_directory_uri() . '/style.css', array(), null );
}
add_action( 'wp_enqueue_scripts', 'child_enqueue_styles', 15 );

/**
 * Load ACF field groups and post types from local files
 */
function load_acf_local_files() {
    // Load ACF field groups
    $acf_path = get_stylesheet_directory() . '/acf-json/';
    if (is_dir($acf_path)) {
        add_filter('acf/settings/load_json', function($paths) use ($acf_path) {
            $paths[] = $acf_path;
            return $paths;
        });
    }
}
add_action('acf/init', 'load_acf_local_files');

/**
 * Include ACF export functionality
 */
if (is_admin()) {
    require_once get_stylesheet_directory() . '/export-acf.php';
}

/**
 * Set ACF to save field groups to local JSON files automatically
 */
add_filter('acf/settings/save_json', function($path) {
    return get_stylesheet_directory() . '/acf-json';
});

/**
 * Add custom save path for ACF field groups
 */
add_filter('acf/settings/load_json', function($paths) {
    // Remove original path (optional)
    unset($paths[0]);

    // Add new path
    $paths[] = get_stylesheet_directory() . '/acf-json';

    return $paths;
});

function remove_global_css() {
	remove_action( 'wp_enqueue_scripts', 'wp_enqueue_global_styles' );
	remove_action( 'wp_body_open', 'wp_global_styles_render_svg_filters' );
	remove_action( 'wp_footer', 'wp_enqueue_global_styles', 1 );
}
add_action('init', 'remove_global_css');

// Shortcode for dynamic year - use [year] in footer or as needed
function year_shortcode() {
  $year = date('Y');
  return $year;
}
add_shortcode('year', 'year_shortcode');

/* Update default GF error message gform_validation_message_FORMNUMBER */
add_filter( 'gform_validation_message_1', function ( $message, $form ) {
    if ( gf_upgrade()->get_submissions_block() ) {
        return $message;
    }
 
    $message = "<h2 class='gform_submission_error hide_summary'>Email address is required. Please <NAME_EMAIL> format</h2>";
   
 
    return $message;
}, 10, 2 );