/*!
Theme Name: Ka<PERSON> Child
Theme URI: https://www.kadencewp.com/kadence-theme/
Template: kadence
Author: Kadence WP
Author URI: https://www.kadencewp.com/
Description: A child theme for the Kadence Theme.
Version: 1.0.0
License: GNU General Public License v3.0 (or later)
License URI: https://www.gnu.org/licenses/gpl-3.0.html
Text Domain: kadence-child
*/


/* A11y Fixes */

/* Allow for esc key to close Kadence submenu */
.nav--toggle-sub li:hover > ul,
.nav--toggle-sub li.menu-item--toggled-on > ul,
.nav--toggle-sub li:not(.menu-item--has-toggle):focus > ul {
 display: none;
}

.main-navigation .menu-item .sub-menu {
  display: none;
}
.main-navigation .menu-item .sub-menu.submenu-open,
.main-navigation .menu-item .sub-menu.toggle-show {
  display: block;
}

/* Gravity Forms */
.gform_wrapper.gravity-theme input[type=color], .gform_wrapper.gravity-theme input[type=date], .gform_wrapper.gravity-theme input[type=datetime-local], .gform_wrapper.gravity-theme input[type=datetime], .gform_wrapper.gravity-theme input[type=email], .gform_wrapper.gravity-theme input[type=month], .gform_wrapper.gravity-theme input[type=number], .gform_wrapper.gravity-theme input[type=password], .gform_wrapper.gravity-theme input[type=search], .gform_wrapper.gravity-theme input[type=tel], .gform_wrapper.gravity-theme input[type=text], .gform_wrapper.gravity-theme input[type=time], .gform_wrapper.gravity-theme input[type=url], .gform_wrapper.gravity-theme input[type=week], .gform_wrapper.gravity-theme select, .gform_wrapper.gravity-theme textarea {
	font-size:1rem;
}
input[type="text"], input[type="email"], input[type="url"], input[type="password"], input[type="search"], input[type="number"], input[type="tel"], input[type="range"], input[type="date"], input[type="month"], input[type="week"], input[type="time"], input[type="datetime"], input[type="datetime-local"], input[type="color"], textarea {
	color: var(--global-palette3);
}
input[type="text"]:focus, input[type="email"]:focus, input[type="url"]:focus, input[type="password"]:focus, input[type="search"]:focus, input[type="number"]:focus, input[type="tel"]:focus, input[type="range"]:focus, input[type="date"]:focus, input[type="month"]:focus, input[type="week"]:focus, input[type="time"]:focus, input[type="datetime"]:focus, input[type="datetime-local"]:focus, input[type="color"]:focus, textarea:focus, .gform_wrapper.gravity-theme .gfield textarea:focus, select:focus {
	border-color:#000000;
	outline:-webkit-focus-ring-color auto 1px !important;
}
/* Styling for floating label for gravity forms. 
 * Add class of floating-label to desired form fields */
.floating-label {
        position: relative;
}

.floating-label label {
    position: absolute;
    top: 12px;
    left: 0;
    margin: 0;
    opacity: 0;
    line-height: 1.4;
    font-size: 1rem;
    transition: all ease 0.4s;
}

.floating-label:focus-within label,
.floating-label input:focus ~ label, 
.floating-label textarea:focus ~ label,
.floating-label input.valid ~ label,
.floating-label textarea.valid ~ label, 
.floating-label input:visited ~ label,
.floating-label textarea:visited ~ label,
.floating-label input:-webkit-autofill ~ label,
.floating-label textarea:-webkit-autofill ~ label,
.floating-label.input-active label {
        top: 0;
    left: 16px;
    font-size: .875rem !important;
    color: var(--global-palette3);
    opacity: 1;
}

/* Basic A11Y fixes for Kadence Blocks */
body .kt-blocks-modal-link:not(.kb-btn-global-inherit):focus-within {
	outline:inherit;
}

/* Search Bar */
.woocommerce-product-search {
	position:relative;
}
.woocommerce-product-search .screen-reader-text {
	cursor: text;
	 font-size: 1rem;
	 left: 1rem;
	 margin: 0;
	 opacity: 0;
	 padding-right: 3.5em;
	 position: absolute;
	 top: 0.75rem;
	 font-weight: 500;
	 transition: all ease 0.4s;
}
.woocommerce-product-search:focus-within .screen-reader-text {
	clip:unset;
	clip-path:unset;
	height:auto;
	width:auto;
	left: 12px;
    padding: 0 5px !important;
    background: #fff;
    font-size: 13px!important;
    top: -13px;
    left: 1px;
    line-height: 19px;
    color: #383b41;
    opacity: 1;
}
.woocommerce-product-search input[type='search']::placeholder,
.search-form input[type='search']::placeholder {
	opacity:1;
}

/* menu */
body:not(.hide-focus-outline) .header-navigation li.menu-item--has-toggle>a:focus .dropdown-nav-toggle {
	opacity:1 !important;
}
body #search-drawer .drawer-inner form ::-webkit-input-placeholder {
	opacity:1;
}
#search-drawer input:-webkit-autofill,
#search-drawer input:-webkit-autofill:focus {
    transition: background-color 0s 600000s, color 0s 600000s !important;
}

/* Cookies */
.cky-preference-header .cky-btn-close img {
	filter:brightness(0);
}

/* Kadence Slider */
.kb-splide .splide__pagination__page:focus-visible {
	outline:2px solid #000;
}

/* Smart Slider */
body .n2-ss-slider :focus-visible,
body .n2-ss-slider a.n2-ow:focus-visible, 
body .n2-ss-slider .n2-ow-all a:focus-visible {
	outline:1px solid #fff !important;
	box-shadow:inset 0 0 0 1px #000000 !important;
}
.n2-ss-slider .n2-ss-widget.n2-ss-widget-hidden {
	display:none;
}

/* Forms */
body select,
body select.orderby {
	background-image: url("data:image/svg+xml,%3Csvg aria-hidden='true' class='kadence-svg-icon kadence-arrow-down-svg' fill='currentColor' version='1.1' xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath d='M5.293 9.707l6 6c0.391 0.391 1.024 0.391 1.414 0l6-6c0.391-0.391 0.391-1.024 0-1.414s-1.024-0.391-1.414 0l-5.293 5.293-5.293-5.293c-0.391-0.391-1.024-0.391-1.414 0s-0.391 1.024 0 1.414z'%3E%3C/path%3E%3C/svg%3E");
}

input[type="text"], input[type="email"], input[type="url"], input[type="password"], input[type="search"], input[type="number"], input[type="tel"], input[type="range"], input[type="date"], input[type="month"], input[type="week"], input[type="time"], input[type="datetime"], input[type="datetime-local"], input[type="color"], textarea, body select, body .select2-container--default .select2-selection--single, .select2-container--default .select2-selection--single .select2-selection__rendered {
	border-color:#4E5C74;
	color:var(--global-palette3);
}

/* Events Calendar */
.tribe-events .datepicker .day.focused, .tribe-events .datepicker .day:focus, .tribe-events .datepicker .day:hover, .tribe-events .datepicker .month.focused, .tribe-events .datepicker .month:focus, .tribe-events .datepicker .month:hover, .tribe-events .datepicker .year.focused, .tribe-events .datepicker .year:focus, .tribe-events .datepicker .year:hover {
	outline:2px solid #000;
}
.tribe-events-c-search__input-group {
	position:relative;
}
.tribe-events-c-search__input-group label {
	opacity:0;
	transition: all ease 0.4s;
}
.tribe-events-c-search__input-group:focus-within label {
	clip:unset;
	height:auto;
	width:auto;
	opacity:1;
	margin:0;
	top:-40px;
}
.tribe-events .datepicker .next .tribe-events-c-top-bar__datepicker-nav-icon-svg path, .tribe-events .datepicker .prev .tribe-events-c-top-bar__datepicker-nav-icon-svg path, #primary .tribe-events .tribe-common-c-btn-icon--caret-left .tribe-common-c-btn-icon__icon-svg path, #primary .tribe-events .tribe-common-c-btn-icon--caret-right .tribe-common-c-btn-icon__icon-svg path,
.tribe-events .tribe-events-c-nav__next:disabled .tribe-events-c-nav__next-icon-svg path, .tribe-events button.tribe-events-c-nav__next:disabled .tribe-events-c-nav__next-icon-svg path {
	fill:#707070;
}

/* WooCommerce */
select.orderby:focus-visible {
	outline:2px solid #000;
}
.kadence-shop-top-row {
	position:relative;
}
.kadence-shop-top-row .skip-link {
	right:0;
	left:auto;
}
span.required {
	color:red;
}
.form-description {
	font-size:1rem;
}
.kadence-product-gallery-thumbnails.splide.splide--nav>.splide__slider>.splide__track>.splide__list>.splide__slide:focus-visible,
.kb-splide .splide__arrows .splide__arrow:focus-visible{
	box-shadow:0 0 0 3px #000 !important;
	outline:2px solid #fff !important;
}
body .kb-advanced-slide-inner {
	overflow:visible;
}
/* used to display focus outline above, if needed adjust to not apply to general Kadence slider */
/*body .kb-splide .splide__list {
	padding:3px !important;
}
body .kb-splide.splide-initial .splide__list {
	gap:3px;
}*/
.gbtn.focused {
	outline: 2px solid #fff !important;
}
body .woocommerce form .form-row label {
	color:var(--global-palette3);
}
body .wp-element-button:disabled {
	opacity:0.7;
}

/* End A11y Fixes */

.footer-nav {
	padding:0;
	margin:0;
	list-style-type:none;
	display:flex;
	flex-wrap:wrap;
	justify-content:center;
}

.footer-nav li {
	padding:0 0 0 20px;
}

.footer-nav li:not(:last-child)::after {
	content:"\005C";
	margin-left:20px;
	display:inline-block;
	color:var(--global-palette9);
}

.card-news-category {
	position:absolute;
	bottom:0;
	left:0;
	background:var(--global-palette4);
	color:var(--global-palette9);
}

.kb-button {
	position:relative !important;
	padding-bottom:5px !important;
	border-radius:0;
}

.kb-button:after {
	content:'';
	width:100%;
	height:3px;
	position:absolute;
	left:0;
	bottom:-1px;
	background:var(--global-palette1);
}

.kb-button:hover:after,
.kb-button:focus:after{
	background:var(--global-palette4);
}

body .button-all a {
	text-underline-offset:5px;
}

body .button-all a:hover,
body .button-all a:focus {
	text-decoration-color:var(--global-palette1) !important;
}

h1 .Xkt-highlight {
	position:relative;
	display:inline-block;
}

h1 .kt-highlight {
	text-decoration:underline;
	text-decoration-color: var(--global-palette1);
} 

.row-counter .wp-block-kadence-column {
	position:relative;
}

.row-counter .wp-block-kadence-column:not(:last-child)::after {
	content:"";
	position:absolute;
	top:47%;
	right:-10px;
	height:50px;
	width:1px;
	background:var(--global-palette4);
}

@media screen and (max-width: 767px) {
	.row-counter .wp-block-kadence-column:not(:last-child)::after {
		top:125%;
		right:calc( (100% - 50px) / 2 );
		height:1px;
		width:50px;
	}	
}

.form-newsletter {
	max-width:460px;
}

.gform_wrapper.gravity-theme .form-newsletter .gform_fields {
	grid-column-gap:0;
}

.form-newsletter_wrapper.gform_wrapper.gravity-theme input[type=email] {
	padding:14px 20px;
}

.form-newsletter_wrapper.gform_wrapper.gravity-theme .gform-button {
	background:var(--global-palette4);
	color:var(--global-palette9);
	font-family: var(--global-heading-font-family) !important;
	font-weight:500;
	font-size:1.25rem;
	padding:12px 30px 11px 30px;
	border-radius:4px;
	position:relative;
	left:-3px;	
}

@media screen and (max-width: 640px) {
	.form-newsletter_wrapper.gform_wrapper.gravity-theme .gform-button {	
		left:0;
	}
}

.form-newsletter_wrapper.gform_wrapper.gravity-theme .gform-button:hover,
.form-newsletter_wrapper.gform_wrapper.gravity-theme .gform-button:focus {
	background: var(--global-palette1);
	color:var(--global-palette4); 
}

.anchor-links .menu {
	justify-content:flex-start;
}

.anchor-links .menu .menu-item,
.anchor-links .menu .menu-item .kb-link-wrap,
.anchor-links .menu .menu-item .kb-nav-link-content,
.anchor-links .menu .menu-item .kb-nav-item-title-wrap {
	width:100%;
}

.anchor-links .menu .menu-item .kb-nav-item-title-wrap {
	justify-content:space-between;
}

.anchor-links .menu .menu-item .link-media-container {
	overflow:hidden;
}

.anchor-links .menu .menu-item  .kb-nav-label-content {
	max-width:82%;
}

@media (max-width: 1440px) {
    body .row-layout > .kt-row-column-wrap {
        grid-template-columns: minmax(0, 1fr);
    }
	
	body .row-layout > .kt-row-column-wrap > .wp-block-kadence-column:first-child {
		order:2;
	}
	
	body .row-layout > .kt-row-column-wrap > .wp-block-kadence-column:last-child {
		order:1;
	}
}

.hero a:hover,
.hero a:focus {
	text-decoration:none;
}

@media (min-width: 768px) and (max-width: 1024px) {
	body .row-our-values > div > .wp-block-kadence-column:nth-child(2) {
		grid-column: 1 / -1;
	}
}

/* ===== TEAM STYLES ===== */

/* Executive Team Section */
.executive-team-section {
	margin-bottom: 3rem;
	padding-bottom: 2rem;
	border-bottom: 2px solid #e5e5e5;
}

.executive-team-title {
	text-align: center;
	margin-bottom: 2rem;
	font-size: 2.5rem;
	font-weight: bold;
}

.executive-team-grid {
	display: grid;
	gap: 2rem;
	margin-bottom: 2rem;
}

.executive-team-grid.columns-2 {
	grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.executive-team-grid.columns-3 {
	grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.executive-team-grid.columns-4 {
	grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

.executive-team-member {
	text-align: center;
	background: #f9f9f9;
	padding: 1.5rem;
	border-radius: 8px;
	box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.executive-team-member .member-photo img {
	width: 100%;
	height: auto;
	border-radius: 50%;
	max-width: 200px;
	margin-bottom: 1rem;
}

.executive-team-member .member-name {
	font-size: 1.25rem;
	font-weight: bold;
	margin-bottom: 0.5rem;
	color: #333;
}

.executive-team-member .member-title {
	font-size: 1rem;
	color: #666;
	margin-bottom: 0.5rem;
	font-weight: 500;
}

.executive-team-member .member-positions {
	font-size: 0.9rem;
	color: #888;
	font-style: italic;
}

/* Main Team Section */
.team-filters {
	text-align: center;
	margin-bottom: 2rem;
}

.filter-btn {
	background: #f0f0f0;
	border: 1px solid #ddd;
	padding: 0.5rem 1rem;
	margin: 0 0.25rem;
	cursor: pointer;
	border-radius: 4px;
	transition: all 0.3s ease;
}

.filter-btn:hover,
.filter-btn.active {
	background: #007cba;
	color: white;
	border-color: #007cba;
}

.main-team-grid {
	display: grid;
	gap: 2rem;
}

.main-team-grid.columns-2 {
	grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.main-team-grid.columns-3 {
	grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.main-team-grid.columns-4 {
	grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

.main-team-member {
	text-align: center;
	background: #fff;
	padding: 1.5rem;
	border-radius: 8px;
	box-shadow: 0 2px 8px rgba(0,0,0,0.1);
	transition: transform 0.3s ease;
}

.main-team-member:hover {
	transform: translateY(-5px);
}

.main-team-member .member-photo img {
	width: 100%;
	height: auto;
	border-radius: 50%;
	max-width: 200px;
	margin-bottom: 1rem;
}

.main-team-member .member-name {
	font-size: 1.25rem;
	font-weight: bold;
	margin-bottom: 0.5rem;
	color: #333;
}

.main-team-member .member-title {
	font-size: 1rem;
	color: #666;
	margin-bottom: 0.5rem;
	font-weight: 500;
}

.main-team-member .member-positions {
	font-size: 0.9rem;
	color: #888;
	font-style: italic;
}

/* Team Page Specific Styles */
.has-executive-team .executive-team-section {
	margin-bottom: 4rem;
	padding-bottom: 3rem;
	border-bottom: 3px solid #e5e5e5;
}

/* Responsive Styles */
@media (max-width: 768px) {
	.executive-team-grid,
	.main-team-grid {
		grid-template-columns: 1fr !important;
		gap: 1.5rem;
	}

	.executive-team-title {
		font-size: 2rem;
	}

	.executive-team-member,
	.main-team-member {
		padding: 1rem;
	}

	.filter-btn {
		margin: 0.25rem;
		padding: 0.4rem 0.8rem;
		font-size: 0.9rem;
	}
}

